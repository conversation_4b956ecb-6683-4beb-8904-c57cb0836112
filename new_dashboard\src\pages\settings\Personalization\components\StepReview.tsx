"use client";
import React, { forwardRef, useImperative<PERSON><PERSON><PERSON>, useState } from "react";
import { Check<PERSON><PERSON>cle2, Loader2 } from "lucide-react";
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
//import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { PersonalizationFormData } from "../utils/types";
import { useSaveorUpdatePersonalization } from '../apis/personalization-apis';
//import { useFetchPersonalization } from "../apis/personalization-apis";

interface StepReviewProps {
  data: PersonalizationFormData;
  refetch: () => void;
  onSave: () => void;

}

const StepReview = forwardRef(({ data, refetch, onSave }: StepReviewProps, ref) => {
  //const [saving, setSaving] = useState(false);    
  const { mutateAsync: savePersonalization, isPending } = useSaveorUpdatePersonalization();
  //const { refetch } = useFetchPersonalization();
 const navigate = useNavigate();
  useImperativeHandle(ref, () => ({
    handleSave,
  }));

 const handleSave = async () => {
  try {
    const response = await savePersonalization(data);

    if (response?.status) {
      toast.success("Personalization saved successfully!");
        onSave();
      await refetch();
    


    } else {
      toast.error(response?.message || "Failed to save personalization!");
    }
  } catch (error) {
    console.error("Unexpected save error:", error);
    toast.error("Something went wrong while saving personalization. Please try again.");
  }
};

  const rows = Object.entries(data.channelConfigs || {});

  return (
    <div className="flex flex-col gap-6">
      <div>
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
          Review Summary
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          One last check to verify all channels before saving.
        </p>
      </div>

      <Card className="shadow-md border border-gray-200 dark:border-gray-800 bg-white dark:bg-neutral-900">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-semibold">
            Channel Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader className="bg-gray-50 dark:bg-neutral-800/60">
              <TableRow>
                <TableHead>Channel</TableHead>
                <TableHead>Primary KPI</TableHead>
                <TableHead>Secondary KPI</TableHead>
                <TableHead>Tertiary KPI</TableHead>
                <TableHead>Weights</TableHead>
                <TableHead>Targets Set</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {rows.length > 0 ? (
                rows.map(([channel, config]) => (
                  <TableRow key={channel}>
                    <TableCell className="font-medium capitalize">
                      {channel}
                    </TableCell>
                    <TableCell>{config.primaryKPI || "-"}</TableCell>
                    <TableCell>{config.secondaryKPI || "-"}</TableCell>
                    <TableCell>{config.tertiaryKPI || "-"}</TableCell>
                    <TableCell>
                      {config.weighting
                        ? `${config.weighting.primary} / ${config.weighting.secondary} / ${config.weighting.tertiary}`
                        : "-"}
                    </TableCell>
                    <TableCell>
                      {config.benchmarks?.length
                        ? `${config.benchmarks.length} month(s)`
                        : "0 month(s)"}
                    </TableCell>
                    <TableCell className="text-green-600 font-medium flex items-center gap-1">
                      <CheckCircle2 className="h-4 w-4" /> Complete
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={7}
                    className="text-center text-gray-500 py-6"
                  >
                    No channel data found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/*<div className="flex justify-end pt-4">
        <Button disabled={saving} className="bg-[#7F56D9] text-white">
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" /> Saving...
            </>
          ) : (
            "Save"
          )}
        </Button>
      </div>*/}
    </div>
  );
});

StepReview.displayName = "StepReview";

export default StepReview;
